"""
Menu and navigation texts for English language
"""

TEXTS = {
    "select_option": "Select one of the following options 👇",
    "select_model": (
        "*🧠 Select a model:*\n\n"
        "🔮 *GPT-4.1*\n"
        "• The newest model from OpenAI\n"
        "• One of the most powerful models in the world\n"
        "• High accuracy and quality of responses\n"
        "• Perfect for complex tasks\n"
        "• 👁️ Support for image analysis\n\n"
        "⚡️ *GPT-4.1-nano*\n"
        "• Ultra-lightweight version of GPT-4.1\n"
        "• Extremely fast and efficient responses\n"
        "• Ideal for simple everyday tasks\n"
        "• Optimal balance of speed and quality\n"
        "• 👁️ Basic support for image analysis\n\n"
        "🔍 *SearchGPT*\n"
        "• GPT-4.1 with internet access\n"
        "• Search for up-to-date information online\n"
        "• Always fresh and verified data\n"
        "• 👁️ Support for image analysis\n\n"
        "🌟 *DeepSeek V3-0324*\n"
        "• The newest model from Chinese company DeepSeek\n"
        "• One of the most powerful AI models in the world\n"
        "• Deep understanding of context and tasks\n"
        "• Outperforms many existing models\n\n"
        "🧩 *Gemini 2.5 PRO*\n"
        "• The newest model from Google\n"
        "• The most advanced and powerful model in the world right now!\n"
        "• Unique reasoning and thinking capabilities\n"
        "• Knowledge up to 2025\n"
        "• Deep understanding with ability to think"
    ),
    "model_selected": "You have selected the model: {model_name}",
    "model_error": "An error occurred while selecting the model",
    "select_role": (
        "*🎭 Select a role for the bot:*\n\n"
        "👤 *Default*\n"
        "• Friendly assistant\n"
        "• Answers to the point\n"
        "• Communicates in English\n\n"
        "👩🏻‍💼 *Emily*\n"
        "• Mysterious and attractive conversationalist\n"
        "• Communication with soul and sincerity\n"
        "• Support, inspiration, and interesting conversations\n\n"
        "👨‍🔬 *Isaac*\n"
        "• Scientific approach\n"
        "• Deep analysis\n"
        "• Accurate and structured answers\n\n"
        "📚 *Tutor*\n"
        "• Help with homework\n"
        "• Detailed problem solutions\n"
        "• Works with all subjects\n\n"
        "💻 *Programmer*\n"
        "• Experienced developer with 10+ years of experience\n"
        "• Help with code in any programming languages\n"
        "• Architectural solutions and best practices\n"
        "• Debugging, optimization, and code review"
    ),
    "role_selected": "You have selected the role: {display_name}",
    "models_info": "ℹ️ Select a model from the list above",
    "select_image_model": (
        "*🎨 Select an image generation model:*\n\n"
        "🚀 *Flux*\n"
        "• Revolutionary next-generation neural network\n"
        "• Incredible detail and photorealistic quality\n"
        "• Lightning-fast generation of premium images\n"
        "• Superior understanding of complex artistic styles\n"
        "• Perfect rendering of lighting, shadows, and textures\n\n"
        "🚀 *GPT-image-1*\n"
        "• Latest neural network for image generation\n"
        "• Generates images in 30–60 seconds\n"
        "• Deep understanding of artistic styles and instructions\n"
        "• Excellent at rendering text on images"
    )
}
